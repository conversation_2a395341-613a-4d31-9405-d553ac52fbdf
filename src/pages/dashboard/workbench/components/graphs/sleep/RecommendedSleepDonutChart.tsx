import { useQuery } from '@tanstack/react-query';
import { Card, Select } from 'antd';
import { useState } from 'react';
import { Cell, Legend, Pie, PieChart, ResponsiveContainer, Tooltip } from 'recharts';

import { GetRecommendedSleepAchieved } from '@/api/services/dashboard/sleepGraphService';
import { CircleLoading } from '@/components/loading';
import { useUserToken } from '@/store/userStore';
import getAxiosErrorMessage from '@/utils/getAxiosErrorMessage';
import { ThemeMode } from '#/enum';
import { useSettings } from '@/store/settingStore';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';

function RecommendedSleepAchievedDonutChart() {
  const [period, setPeriod] = useState<'24h' | '7d' | '30d'>('24h');
  const { accessToken } = useUserToken();
  const { themeMode } = useSettings();

  const { data, isLoading, error } = useQuery({
    queryKey: ['recommended-sleep-achieved', period],
    queryFn: () => GetRecommendedSleepAchieved(accessToken || '', period),
  });

  return (
    <Card>
      <div className="flex aspect-square h-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-semibold">Recommended sleep</p>
          <Select
            value={period}
            onChange={(val: '24h' | '7d' | '30d') => setPeriod(val)}
            style={{ width: 200 }}
          >
            <Select.Option value="24h">Last 24 Hours</Select.Option>
            <Select.Option value="7d">Last 7 Days</Select.Option>
            <Select.Option value="30d">Last 30 Days</Select.Option>
          </Select>
        </div>
        <div className="grow">
          <ChartsLoadingWrapper loading={isLoading}>
            {error ? (
              <div className="flex h-full items-center justify-center">
                {getAxiosErrorMessage(error, 'Error loading sleep trends data')}
              </div>
            ) : (
              data && (
                <ResponsiveContainer width="100%" height={'98%'}>
                  <PieChart margin={{ top: 20, right: 0, left: 0, bottom: 0 }}>
                    <Pie
                      data={[
                        { name: 'Goal Achieved', value: data?.goalAchieved },
                        { name: 'Goal Not Achieved', value: data?.totalUsers - data?.goalAchieved },
                      ]}
                      dataKey="value"
                      nameKey="name"
                      fill="#8884d8"
                    >
                      <Cell key={`cell-achieved`} fill="#387160" />
                      <Cell key={`cell-not-achieved`} fill="#FFA500" />
                    </Pie>
                    <Tooltip
                      contentStyle={{
                        backgroundColor: themeMode == ThemeMode.Dark ? '#1f2937' : '#fff',
                        borderColor: themeMode == ThemeMode.Dark ? '#4b5563' : '#ccc',
                        color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                      }}
                      itemStyle={{
                        color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                      }}
                      labelFormatter={(label: string) => `Users: ${label}`}
                    />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              )
            )}
          </ChartsLoadingWrapper>
        </div>
        <div className="flex flex-row items-start justify-end gap-[.5]">
          <p className="text-end text-[.5rem] text-error lg:text-xs">*</p>
          <p className="text-end text-[.5rem] text-gray lg:text-xs">
            As per the minimum recommended sleep by WHO
          </p>
        </div>
      </div>
    </Card>
  );
}

export default RecommendedSleepAchievedDonutChart;
