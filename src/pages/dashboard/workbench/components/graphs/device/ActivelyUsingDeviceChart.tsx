import { useQuery } from '@tanstack/react-query';
import { Select, Card } from 'antd';
import { useState } from 'react';

import { GetActiveUsersApi } from '@/api/services/dashboard/activelyUsingDeviceService';
import { CircleLoading } from '@/components/loading/circle-loading';
import { useUserToken } from '@/store/userStore';
import { Cell, Legend, Pie, PieChart, ResponsiveContainer, Tooltip } from 'recharts';
import { useSettings } from '@/store/settingStore';
import { ThemeMode } from '#/enum';
import getAxiosErrorMessage from '@/utils/getAxiosErrorMessage';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';

const COLORS = ['#387160', '#FF6B6B'];

export const ActivelyUsingDeviceChart = () => {
  const [period, setPeriod] = useState<'24h' | '7d' | '30d'>('24h');
  const { accessToken } = useUserToken();
  const { themeMode } = useSettings();

  const { data, isLoading, error } = useQuery({
    queryKey: ['actively-using-device', period],
    queryFn: () => GetActiveUsersApi(accessToken || '', period),
  });

  return (
    <Card>
      <div className="flex aspect-square h-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-semibold">Actively Using Devices</p>
          <Select
            value={period}
            onChange={(val: '24h' | '7d' | '30d') => setPeriod(val)}
            style={{ width: 200 }}
          >
            <Select.Option value="24h">Last 24 Hours</Select.Option>
            <Select.Option value="7d">Last 7 Days</Select.Option>
            <Select.Option value="30d">Last 30 Days</Select.Option>
          </Select>
        </div>
        <div className="grow">
          <ChartsLoadingWrapper loading={isLoading}>
            {error ? (
              <div className="flex h-full items-center justify-center">
                {getAxiosErrorMessage(error, 'Error loading popular activities data')}
              </div>
            ) : !data ||
              data.filter((item) => item.name == 'Not Actively Using Device')[0].value === 0 ? (
              <ResponsiveContainer width="100%" height="95%">
                <PieChart>
                  <Pie
                    data={[
                      {
                        name: 'Not Actively Using Device',
                        value: 100,
                      },
                    ]}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius="80%"
                  >
                    <Cell
                      key="Not logged"
                      fill={themeMode == ThemeMode.Dark ? '#4b5563' : '#DFE3E8'}
                    />
                  </Pie>
                  <Legend
                    payload={[
                      {
                        value: 'Actively Using Device',
                        type: 'square',
                        color: '#387160',
                      },
                      {
                        value: 'Not Actively Using Device',
                        type: 'square',
                        color: '#FF6B6B',
                      },
                    ]}
                  />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              data && (
                <ResponsiveContainer width="100%" height={'98%'}>
                  <PieChart margin={{ top: 20, right: 0, left: 0, bottom: 0 }}>
                    <Pie
                      data={data.map((item) => ({
                        name: item.name,
                        value: Number(
                          Number(
                            (item.value / data?.reduce((acc, curr) => acc + curr?.value, 0)) * 100,
                          ).toFixed(2),
                        ),
                      }))}
                      dataKey="value"
                      nameKey="name"
                      fill="#8884d8"
                    >
                      {data.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={{
                        backgroundColor: themeMode == ThemeMode.Dark ? '#1f2937' : '#fff',
                        borderColor: themeMode == ThemeMode.Dark ? '#4b5563' : '#ccc',
                        color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                      }}
                      itemStyle={{
                        color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                      }}
                      formatter={(value: number, name: string) => [
                        `${value}%`,
                        name.charAt(0).toUpperCase() + name.slice(1),
                      ]}
                      // labelFormatter={(label: string) => `Users: ${'100%'}`}
                    />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              )
            )}
          </ChartsLoadingWrapper>
        </div>
      </div>
    </Card>
  );
};
