import { useQuery } from '@tanstack/react-query';
import { Select, Spin, Empty, Card } from 'antd';
import React, { useState } from 'react';
import { BarChart, Bar, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from 'recharts';

import { useUserToken } from '@/store/userStore';
import { GetMealTypeLoggedTrend } from '@/api/services/dashboard/nutritionGraphService';
import getAxiosErrorMessage from '@/utils/getAxiosErrorMessage';
import { useSettings } from '@/store/settingStore';
import { ThemeMode } from '#/enum';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';

const { Option } = Select;

const formatLabel = (label: string) => {
  if (label.includes('to')) {
    const [start, end] = label.split(' to ');
    const startDate = new Date(start);
    const endDate = new Date(end);
    const format = (d: Date) => `${d.getDate()} ${d.toLocaleString('default', { month: 'short' })}`;
    return `${format(startDate)} - ${format(endDate)}`;
  } else {
    const date = new Date(label);
    return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}`;
  }
};

const MealTypeLoggedStackedBarChart: React.FC = () => {
  const [period, setPeriod] = useState<'weekly' | 'monthly'>('weekly');
  const { accessToken } = useUserToken();
  const { themeMode } = useSettings();

  const { data, isLoading, error } = useQuery({
    queryKey: ['meal-type-logged-trends', period],
    queryFn: () => GetMealTypeLoggedTrend(accessToken || '', period),
  });

  const handlePeriodChange = (value: 'weekly' | 'monthly') => {
    setPeriod(value);
  };

  return (
    <Card>
      <div className="flex aspect-square h-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-semibold">Meal Type Trends</p>

          <Select value={period} onChange={handlePeriodChange} style={{ width: 200 }}>
            <Option value="weekly">Weekly</Option>
            <Option value="monthly">Monthly</Option>
          </Select>
        </div>

        <div className="grow">
          <ChartsLoadingWrapper loading={isLoading}>
            {error ? (
              <Empty description={getAxiosErrorMessage(error, 'Error loading meal trends')} />
            ) : data?.length === 0 ? (
              <Empty description="No data available" />
            ) : (
              <ResponsiveContainer width="100%" height={400}>
                <BarChart
                  data={data?.map((item: any) => ({
                    name: formatLabel(item.period),
                    ...item.mealTypeUserCounts,
                  }))}
                >
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip
                    // content={<CustomTooltip />}
                    cursor={{ fill: 'transparent' }}
                    wrapperStyle={
                      {
                        // backgroundColor: 'rgba(0,0,0,1)',
                      }
                    }
                    contentStyle={{
                      backgroundColor: themeMode == ThemeMode.Dark ? '#1f2937' : '#fff',
                      borderColor: themeMode == ThemeMode.Dark ? '#4b5563' : '#ccc',
                      color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                    }}
                  />
                  <Legend />
                  <Bar dataKey="Breakfast" stackId="a" fill="#387160" name="Breakfast" />
                  <Bar
                    dataKey="Afternoon Snack"
                    stackId="a"
                    fill="#8884d8"
                    name="Afternoon Snack"
                  />
                  <Bar dataKey="Lunch" stackId="a" fill="#ff7300" name="Lunch" />
                  <Bar dataKey="Dinner" stackId="a" fill="#888888" name="Dinner" />
                  <Bar
                    dataKey="Late Night Snack"
                    stackId="a"
                    fill="#264E86FF"
                    name="Late Night Snack"
                  />
                  <Bar
                    dataKey={'Mid Morning Snack'}
                    stackId="a"
                    fill="#ffc658"
                    name="Mid Morning Snack"
                  />
                </BarChart>
              </ResponsiveContainer>
            )}
          </ChartsLoadingWrapper>
        </div>
      </div>
    </Card>
  );
};

export default MealTypeLoggedStackedBarChart;
