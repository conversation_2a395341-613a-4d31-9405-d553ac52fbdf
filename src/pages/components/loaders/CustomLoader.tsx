import { useEffect, useRef } from 'react';
import lottie from 'lottie-web';
import Loader from '../../../assets/loaders/loaderDarkPurple.json';

const CustomLoader = ({ speed = 2, className = 'w-32 h-32' }) => {
  const containerRef = useRef(null);

  useEffect(() => {
    if (!containerRef.current) {
      return;
    }

    const animation = lottie.loadAnimation({
      container: containerRef.current,
      renderer: 'svg',
      loop: true,
      autoplay: true,
      animationData: Loader,
    });

    animation.setSpeed(speed);

    return () => {
      animation.destroy();
    };
  }, [speed]);

  return <div ref={containerRef} className={className} />;
};

export default CustomLoader;
