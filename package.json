{"name": "appetec", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "prepare": "husky install"}, "dependencies": {"@ant-design/cssinjs": "^1.17.2", "@ant-design/icons": "^5.2.6", "@fullcalendar/common": "^5.11.5", "@fullcalendar/core": "^6.1.9", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/interaction": "^6.1.9", "@fullcalendar/list": "^6.1.9", "@fullcalendar/react": "^6.1.9", "@fullcalendar/timegrid": "^6.1.9", "@fullcalendar/timeline": "^6.1.9", "@iconify/react": "^5.0.2", "@tanstack/react-query": "^5.50.1", "@tanstack/react-query-devtools": "^5.50.1", "@vercel/analytics": "^1.2.2", "@vitejs/plugin-react": "^4.1.0", "antd": "^5.21.6", "apexcharts": "^4.0.0", "appetec": "link:", "autosuggest-highlight": "^3.3.4", "axios": "^1.5.1", "classnames": "^2.3.2", "color": "^4.2.3", "dayjs": "^1.11.10", "deps": "^1.0.0", "framer-motion": "^11.11.10", "highlight.js": "^11.9.0", "i18next": "^23.16.4", "i18next-browser-languagedetector": "^8.0.0", "jwt-decode": "^4.0.0", "lottie-react": "^2.4.1", "lottie-web": "^5.13.0", "moment": "^2.30.1", "nprogress": "^0.2.0", "numeral": "^2.0.6", "ramda": "^0.30.1", "react": "^18.3.1", "react-apexcharts": "^1.5.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-error-boundary": "^4.1.2", "react-helmet-async": "^2.0.5", "react-i18next": "^15.1.0", "react-icons": "^5.3.0", "react-markdown": "^9.0.1", "react-organizational-chart": "^2.2.1", "react-quill": "^2.0.0", "react-router-dom": "^6.27.0", "react-use": "^17.4.0", "recharts": "^2.15.3", "rehype-highlight": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "reset-css": "^5.0.2", "screenfull": "^6.0.2", "simplebar-react": "^3.2.4", "styled-components": "^6.0.9", "zustand": "^5.0.0"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@faker-js/faker": "^9.1.0", "@types/autosuggest-highlight": "^3.2.0", "@types/color": "^4.2.0", "@types/jwt-decode": "^3.1.0", "@types/nprogress": "^0.2.1", "@types/numeral": "^2.0.3", "@types/ramda": "^0.30.2", "@types/react": "^18.2.28", "@types/react-beautiful-dnd": "^13.1.6", "@types/react-dom": "^18.2.13", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.28", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.12.2", "autoprefixer": "^10.4.16", "eslint": "^9.27.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.6", "lint-staged": "^15.2.10", "msw": "^2.6.0", "postcss": "^8.4.31", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.1", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.80.5", "stylelint": "^16.14.1", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^14.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.7.0", "stylelint-order": "^6.0.3", "stylelint-scss": "^6.11.0", "tailwindcss": "^3.4.14", "terser": "^5.36.0", "ts-node": "^10.9.1", "typescript": "^5.6.3", "vite": "^5.4.10", "vite-plugin-svg-icons": "^2.0.1", "vite-tsconfig-paths": "^5.0.1"}, "msw": {"workerDirectory": "public"}, "packageManager": "pnpm@10.12.3+sha512.467df2c586056165580ad6dfb54ceaad94c5a30f80893ebdec5a44c5aa73c205ae4a5bb9d5ed6bb84ea7c249ece786642bbb49d06a307df218d03da41c317417"}